module Brokerages
  class BrokerageInfo
    include ActionView::Helpers::NumberHelper

    ADDRESS_TEMPLATE = '%<city>s, %<state>s %<zip>s'.freeze

    attr_reader :brokerage

    delegate :zip, to: :brokerage
    delegate :access_to_feature?, :banner, :logo, :website_url, :claimed?, :hide_street, to: :profile

    delegate_missing_to :brokerage

    def initialize(brokerage)
      @brokerage = brokerage
    end

    alias entity brokerage

    def assets
      @assets ||= access_to_feature?(:assets) ? profile.assets.rank(:row_order) : BrokerageProfileAsset.none
    end

    def images
      @images ||= assets.select { |asset| asset.asset_type == 'image' }
    end

    def documents
      @documents ||= assets.select { |asset| asset.asset_type == 'document' }
    end

    def address
      format(ADDRESS_TEMPLATE, city: city.name, state: city.state_code, zip:)
    end

    def authorities
      @authorities ||= Brokerages::Authorities.for(brokerage).then do |auths|
        auths.flat_map(&:statuses).sort.presence || [auths.map(&:null_status).min]
      end
    end

    def authority
      @authority ||= authorities.first
    end

    def bio
      profile.bio.presence || Brokerages::GenerateBio.call(brokerage)
    end

    def city
      @city ||= Companies::PreferredCity.new(brokerage)
    end

    def docket_numbers
      operating_authorities.pluck(:docket_number).join(', ')
    end

    def authority_numbers
      operating_authorities.pluck(:docket_number).push("USDOT #{dot_number}")
    end

    def insurances
      @insurances ||= brokerage.insurances.includes(:insurance_company).sort_by(&:policy_limit).reverse
    end

    def insurance_limit
      return 'None' if brokerage.insurances.blank?

      number_to_currency(brokerage.insurances.max_by(&:policy_limit).policy_limit, precision: 0)
    end

    def review_count
      reviews_aggregate.review_count.to_i
    end

    def star_rating
      reviews_aggregate.star_rating.to_d
    end

    def reviews_aggregate
      @reviews_aggregate ||= brokerage.reviews_aggregate || Nullable.object(:reviews_aggregate)
    end

    def street_address
      Functions::FormatStreetAddress.call(brokerage.street)
    end

    private

    def profile
      brokerage.profile || Nullable.object(:brokerage_profile)
    end
  end
end
