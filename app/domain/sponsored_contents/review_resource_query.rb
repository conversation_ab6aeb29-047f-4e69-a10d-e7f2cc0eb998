module SponsoredContents
  class ReviewResourceQuery
    include Callable

    QUERY = <<~SQL.squish
      select bp.company_id as id, 'broker' as entity
      from brokerage_profiles bp
               inner join access_packages ap
                          on ap.resource_id = bp.id and ap.resource_type = 'BrokerageProfile'
               inner join brokerage_reviews br on br.company_id = bp.company_id
      where ap.active = true
        and ap.features @> '{sponsored_content}'
        and br.featured = true
      union
      select cp.company_id as id, 'carrier' as entity
      from carrier_profiles cp
               inner join access_packages ap
                          on ap.resource_id = cp.id and ap.resource_type = 'CarrierProfile'
               inner join reviews r on r.company_id = cp.company_id
      where ap.active = true
        and ap.features @> '{sponsored_content}'
        and r.featured = true
      order by id;
    SQL

    def call
      ApplicationRecord.connection.exec_query(QUERY)
    end
  end
end
