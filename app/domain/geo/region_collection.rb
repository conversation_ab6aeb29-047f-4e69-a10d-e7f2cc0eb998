module Geo
  class RegionCollection
    include Enumerable

    attr_reader :country, :regions

    def initialize(country:, regions:)
      @country = country
      @regions = regions
    end

    def each(&)
      return enum_for(:each) unless block_given?

      regions.each(&)
    end

    def where(**opts)
      case opts
      in { id: ids }
        Array.wrap(ids).map { |id| indexed_by_id[id] }
      in { slug: slugs }
        Array.wrap(slugs).map { |slug| indexed_by_slug[slug] }
      else
        raise ArgumentError, "Cannot find regions by #{opts.inspect}"
      end
    end

    def find_by(...)
      where(...).first
    end

    private

    def indexed_by_id
      @indexed_by_id ||= regions.index_by(&:id)
    end

    def indexed_by_slug
      @indexed_by_slug ||= regions.index_by(&:slug)
    end
  end
end
