module PageViews
  class FetchAndStoreJob
    include Sidekiq::Job

    # rubocop:disable Metrics/ParameterLists
    def perform(uuid = SecureRandom.uuid, date = 2.days.ago.to_date.strftime, filter = 'all',
                limit = 10_000, offset = 0)
      response = PageViews::FetchAndStore.call(uuid:, date:, filter:, limit:, offset:)
      new_offset = calc_offset(response, limit, offset)

      if new_offset.blank?
        PageViews::RedisStore.flush(uuid)
      else
        PageViews::FetchAndStoreJob.perform_async(uuid, date, filter, limit, new_offset)
      end
    end
    # rubocop:enable Metrics/ParameterLists

    private

    def calc_offset(response, limit, offset)
      row_count = limit + offset
      row_count < response.row_count ? row_count : nil
    end
  end
end
