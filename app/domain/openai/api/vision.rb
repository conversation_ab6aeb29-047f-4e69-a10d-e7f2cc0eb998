module OpenAI
  module Api
    class Vision
      include OpenAI::Api::Base

      def analyze_image(image_url:, prompt:, model: 'gpt-4o', max_tokens: 2000)
        post '/chat/completions', json: {
          model: model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: image_url
                  }
                }
              ]
            }
          ],
          max_tokens: max_tokens
        }
      end

      def analyze_image_with_base64(image_data:, prompt:, model: 'gpt-4o', max_tokens: 2000)
        post '/chat/completions', json: {
          model: model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: "data:image/jpeg;base64,#{image_data}"
                  }
                }
              ]
            }
          ],
          max_tokens: max_tokens
        }
      end
    end
  end
end
