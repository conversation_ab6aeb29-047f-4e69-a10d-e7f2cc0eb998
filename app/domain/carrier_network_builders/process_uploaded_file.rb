module CarrierNetworkBuilders
  class ProcessUploadedFile
    include Callable

    LANE_EXTRACTION_PROMPT = <<~PROMPT.freeze
      You are an AI assistant that extracts shipping lane information from uploaded files. 
      
      Your task is to analyze the content and extract shipping lane requirements. Look for:
      
      REQUIRED INFORMATION:
      1. Origin (city, state, or region in the US)
      2. Destination (city, state, or region in the US)
      
      OPTIONAL INFORMATION:
      3. Truck Type: #{Records[:truck_types].all.map(&:slug).join(', ')}
      4. Shipment Type: #{Records[:shipment_types].all.map(&:slug).join(', ')}
      5. Specialized Services: #{Records[:specialized_services].all.map(&:slug).join(', ')}
      6. Freights: #{Records[:freights].all.map(&:slug).join(', ')}
      7. Volume (free-form text)
      8. Frequency (free-form text)
      
      SMART INFERENCE RULES:
      - When you see freight types, automatically infer the appropriate truck type, shipment type, and specialized services using SLUGS:
        * Refrigerated Food, Meat, frozen/cold items → reefer truck type, refrigerated-food freight
        * Motor Vehicles, cars, vehicles → auto-carrier truck type, motor-vehicles freight
        * Liquids/Gases, fuel, chemicals → tanker truck type, liquids-gases freight, hazardous-materials specialized service if hazmat
        * Heavy machinery, construction equipment → lowboy truck type, heavy-machinery freight
        * General freight, dry goods → dry-van truck type, general-freight freight
        * Flatbed items, lumber, steel → flatbed truck type, building-materials freight
      
      LOCATION FORMAT:
      - Use hierarchical format: 'country:state:city' for cities, 'country:state' for states, 'country:region' for regions
      - Examples: 'united-states:illinois:chicago', 'united-states:california', 'united-states:west'
      
      Return your response as a JSON array of lane objects in this EXACT format:
      ```json
      [
        {
          "origin_id": "hierarchical_location_format",
          "destination_id": "hierarchical_location_format",
          "filters": {
            "truck_type": ["truck_type_slug"],
            "shipment_type": ["shipment_type_slug"],
            "specialized_service": ["service_slug"],
            "freight": ["freight_slug"]
          },
          "volume": "volume description or null",
          "frequency": "frequency description or null"
        }
      ]
      ```
      
      IMPORTANT: 
      - Use actual slugs (like reefer, dry-van, flatbed, auto-carrier, full-truckload, less-than-truckload, hazardous-materials, team-drivers) NOT names in quotes
      - If multiple lanes are found, return multiple objects in the array
      - If no clear lane information is found, return an empty array []
      - Use null for missing volume/frequency information, not empty strings
    PROMPT

    attr_reader :builder, :file_blob

    def initialize(builder, file_blob)
      @builder = builder
      @file_blob = file_blob
    end

    def call
      content_type = file_blob.content_type
      
      if image_file?(content_type)
        process_image_file
      elsif csv_file?(content_type)
        process_csv_file
      else
        raise ArgumentError, "Unsupported file type: #{content_type}"
      end
    end

    private

    def image_file?(content_type)
      %w[image/jpeg image/jpg image/png image/webp].include?(content_type)
    end

    def csv_file?(content_type)
      %w[text/csv application/csv].include?(content_type) || file_blob.filename.to_s.end_with?('.csv')
    end

    def process_image_file
      # Convert image to base64 for OpenAI Vision API
      image_data = Base64.strict_encode64(file_blob.download)
      
      client = OpenAI::Api::Vision.new(raise_errors: true)
      response = client.analyze_image_with_base64(
        image_data: image_data,
        prompt: LANE_EXTRACTION_PROMPT
      )
      
      extract_lanes_from_response(response)
    end

    def process_csv_file
      csv_content = file_blob.download
      
      # Use regular chat completion for CSV processing
      client = OpenAI::Api::ChatCompletion.new(raise_errors: true)
      response = client.create(
        messages: [
          {
            role: 'user',
            content: "#{LANE_EXTRACTION_PROMPT}\n\nHere is the CSV content to analyze:\n\n#{csv_content}"
          }
        ],
        model: 'gpt-4o-mini',
        temperature: 0.3,
        max_tokens: 2000
      )
      
      extract_lanes_from_response(response)
    end

    def extract_lanes_from_response(response)
      content = response.parse.dig('choices', 0, 'message', 'content')
      
      # Extract JSON from markdown code blocks if present
      json_match = content.match(/```json\s*(.*?)```/m)
      json_content = json_match ? json_match[1] : content
      
      begin
        lanes_data = JSON.parse(json_content)
        Array.wrap(lanes_data).map { |lane_data| format_lane_data(lane_data) }
      rescue JSON::ParserError => e
        Rails.logger.error "Failed to parse AI response as JSON: #{e.message}"
        Rails.logger.error "Response content: #{content}"
        []
      end
    end

    def format_lane_data(data)
      {
        **data.except('truck_type', 'shipment_type', 'specialized_service', 'freight').transform_keys(&:to_sym),
        filters: data.slice('truck_type', 'shipment_type', 'specialized_service', 'freight').transform_keys(&:to_sym)
      }
    end
  end
end
