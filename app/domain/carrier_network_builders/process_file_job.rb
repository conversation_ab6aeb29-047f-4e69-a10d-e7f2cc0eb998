module CarrierNetworkBuilders
  class ProcessFileJob
    include Sidekiq::Job

    sidekiq_options queue: :critical, retry: 3

    def perform(builder_id, file_blob_id)
      builder = CarrierNetworkBuilder.find_by(id: builder_id)
      return unless builder

      file_blob = ActiveStorage::Blob.find_by(id: file_blob_id)
      return unless file_blob

      begin
        # Add processing message
        processing_message = builder.messages.create!(
          role: 'assistant',
          content: 'Processing your uploaded file... This may take a moment.'
        )

        # Process the file
        lanes_data = CarrierNetworkBuilders::ProcessUploadedFile.call(builder, file_blob)

        if lanes_data.any?
          # Create lanes from the extracted data
          created_lanes = lanes_data.map do |lane_data|
            CarrierNetworkBuilders::UpsertLane.call(builder, lane_data)
          end

          # Create success message
          lane_count = created_lanes.size
          success_message = if lane_count == 1
            "Great! I found 1 shipping lane in your file and added it to your network builder."
          else
            "Excellent! I found #{lane_count} shipping lanes in your file and added them to your network builder."
          end

          builder.messages.create!(
            role: 'assistant',
            content: success_message
          )

          # Broadcast the update via Turbo Stream
          broadcast_file_processing_complete(builder, created_lanes, success_message)
        else
          # No lanes found
          error_message = "I couldn't find any clear shipping lane information in your file. Please make sure the file contains origin and destination information, and try uploading again or describe your shipping needs in the chat."
          
          builder.messages.create!(
            role: 'assistant',
            content: error_message
          )

          broadcast_file_processing_complete(builder, [], error_message)
        end

        # Remove the processing message
        processing_message.destroy

      rescue StandardError => e
        Rails.logger.error "File processing failed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")

        # Create error message
        error_message = "I'm sorry, but I encountered an error while processing your file. Please try again or describe your shipping needs in the chat."
        
        builder.messages.create!(
          role: 'assistant',
          content: error_message
        )

        broadcast_file_processing_complete(builder, [], error_message)

        # Remove the processing message if it still exists
        processing_message&.destroy
      ensure
        # Clean up the uploaded file
        file_blob&.purge_later
      end
    end

    private

    def broadcast_file_processing_complete(builder, lanes, message_content)
      # This would broadcast via Turbo Stream to update the UI
      # For now, we'll rely on the message creation to trigger UI updates
      # In a future enhancement, we could add real-time updates here
    end
  end
end
