module CarrierNetworkBuilders
  module ChatResponseHandlers
    class FileUpload < Base
      attr_reader :file_blob

      def initialize(builder:, response: nil, file_blob: nil)
        super(builder: builder, response: response)
        @file_blob = file_blob
      end

      def handle?
        file_blob.present?
      end

      def message
        { 
          role: 'assistant', 
          content: "I've received your file. Let me analyze it to extract shipping lane information..." 
        }
      end

      def lanes
        # Queue the file processing job
        CarrierNetworkBuilders::ProcessFileJob.perform_async(builder.id, file_blob.id)
        
        # Return empty array since lanes will be created asynchronously
        []
      end
    end
  end
end
