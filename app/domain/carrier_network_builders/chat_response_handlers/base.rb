module CarrierNetworkBuilders
  module ChatResponseHandlers
    class Base
      attr_reader :builder, :response

      def initialize(builder:, response:)
        @builder = builder
        @response = response
      end

      def handle?
        true
      end

      def content
        @content ||= response.parse.dig('choices', 0, 'message', 'content')
      end

      def message
        { role: 'assistant', content: }
      end

      def lanes
        []
      end
    end
  end
end
