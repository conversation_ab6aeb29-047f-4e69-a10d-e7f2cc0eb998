module CarrierNetworkBuilders
  module ChatResponseHandlers
    class HandleResponse
      include Callable

      HANDLERS = %w(FileUpload LaneCreation Base).freeze

      Result = Data.define(:message, :lanes)

      attr_reader :builder, :response

      def initialize(builder:, response:)
        @builder = builder
        @response = response
      end

      def call
        message = builder.messages.create!(handler.message)
        lanes = handler.lanes.map { |lane| CarrierNetworkBuilders::UpsertLane.call(builder, lane) }
        Result.new(message:, lanes:)
      end

      private

      def handler
        HANDLERS.lazy
          .map { |name| CarrierNetworkBuilders::ChatResponseHandlers.const_get(name, false).new(builder:, response:) }
          .find(&:handle?)
      end
    end
  end
end
