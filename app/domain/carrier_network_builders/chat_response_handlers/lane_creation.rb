module CarrierNetworkBuilders
  module ChatResponseHandlers
    class LaneCreation < Base
      JSON_BLOCK_REGEX = /```json\s+(.*?)```/m

      FILTER_KEYS = %w(truck_type shipment_type specialized_service freight).freeze

      def handle?
        JSON_BLOCK_REGEX.match?(content)
      end

      def lanes
        content[JSON_BLOCK_REGEX, 1].then { |json| JSON.parse(json) }.then { |data| format_data(data) }
      rescue JSON::ParserError
        super
      end

      def message
        { role: 'assistant', content: 'Based on your requirements, I have created a lane for you.' }
      end

      private

      def format_data(data)
        Array.wrap(data).map do |entry|
          {
            **entry.except(*FILTER_KEYS).transform_keys(&:to_sym),
            filters: entry.slice(*FILTER_KEYS).transform_keys(&:to_sym)
          }
        end
      end
    end
  end
end
