module Users
  module Personas
    extend Dry::Container::Mixin

    namespace 'scopes' do
      register(:any) { ::Persona.types.keys }
      register(:contacts) { %w(broker shipper) }
      register(:standard) { %w(broker shipper dispatcher seller) }

      ::Persona.types.each_key do |type|
        register(type) { [type] }
      end
    end

    def self.create(user, **attributes)
      constant = "Create#{attributes[:persona].to_s.classify}"
      Users::Personas.const_get(constant, false).call(user, **attributes)
    end

    def self.find(user:, scope: :any)
      return unless key?("scopes.#{scope}")

      resolve("scopes.#{scope}").then do |personas|
        user.personas.find { |persona| personas.include?(persona.type) }
      end
    end
  end
end
