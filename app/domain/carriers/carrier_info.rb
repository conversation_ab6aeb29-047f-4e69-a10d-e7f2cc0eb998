module Carriers
  class CarrierInfo
    include ActionView::Helpers::NumberHelper

    ADDRESS_TEMPLATE = '%<city>s, %<state>s %<zip>s'.freeze

    attr_reader :carrier, :company

    delegate :zip, to: :carrier
    delegate :access_to_feature?, :banner, :logo, :lanes_map, :claimed?, :hide_street, to: :profile

    delegate_missing_to :carrier

    def initialize(carrier)
      @carrier = carrier
      @company = @carrier.company
    end

    alias entity carrier

    def address
      format(ADDRESS_TEMPLATE, city: city.name, state: city.state_code, zip:)
    end

    def authorities
      @authorities ||= Carriers::Authorities.for(carrier).then do |auths|
        auths.flat_map(&:statuses).sort.presence || [auths.map(&:null_status).min]
      end
    end

    def authority
      @authority ||= authorities.first
    end

    def bio
      profile.bio.presence || Carriers::GenerateBio.call(carrier)
    end

    def city
      @city ||= Companies::PreferredCity.new(carrier)
    end

    def docket_numbers
      carrier.operating_authorities.pluck(:docket_number).join(', ')
    end

    def authority_numbers
      carrier.operating_authorities.pluck(:docket_number).push("USDOT #{dot_number}")
    end

    def fleet_size
      carrier.power_units
    end

    def insurance_limit
      return 'None' if carrier.insurances.blank?

      number_to_currency(carrier.insurances.max_by(&:policy_limit).policy_limit, precision: 0)
    end

    def review_count
      reviews_aggregate.review_count.to_i
    end

    def safety_rating
      return 'None' if carrier.safety_rating.blank?
      carrier.safety_rating.capitalize
    end

    def star_rating
      reviews_aggregate.star_rating.to_d
    end

    def reviews_aggregate
      @reviews_aggregate ||= carrier.reviews_aggregate || Nullable.object(:reviews_aggregate)
    end

    def website_url
      @website_url ||= Addressable::URI.heuristic_parse(profile.website_url).to_s.presence
    end

    def related_companies
      @related_companies ||= carrier.related_companies.merge(Carrier.indexable)
    end

    def street_address
      Functions::FormatStreetAddress.call(carrier.street)
    end

    private

    def profile
      carrier.profile || Nullable.object(:carrier_profile)
    end
  end
end
