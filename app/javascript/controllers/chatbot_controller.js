import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['input', 'messages', 'fileInput']

  connect() {
    if (this.hasInputTarget) {
      this.inputTarget.focus()
    }
  }

  async sendMessage() {
    const message = this.inputTarget.value.trim()
    const hasFile = this.hasFileInputTarget && this.fileInputTarget.files.length > 0

    if (!message && !hasFile) return

    if (hasFile) {
      this.handleFileUpload()
    } else {
      this.inputTarget.value = ''
      this.addMessage(message)
      this.setLoading()
    }
  }

  addMessage(content) {
    const messageDiv = document.createElement('div')
    messageDiv.className = 'flex gap-3'

    messageDiv.innerHTML = `
        <div class="flex-auto"></div>
        <div class="ml-12 rounded-lg bg-gray-100 p-3 max-w-[80%]">
          ${this.escapeHtml(content)}
        </div>
      `

    this.messagesTarget.appendChild(messageDiv)
    this.scrollToBottom()
  }

  setLoading() {
    const typingDiv = document.createElement('div')
    typingDiv.className = 'flex gap-3'
    typingDiv.id = 'typing-indicator'
    typingDiv.innerHTML = `
        <div class="flex h-8 w-8 flex-none items-center justify-center rounded-full fancy-border-outline">
          ${this.getLogoSvg()}
        </div>
        <div class="mr-12 rounded-lg to-purple-100 p-3 bg-linear-90 from-primary-100">
          <p class="text-gray-600">Thinking...</p>
        </div>
      `
    this.messagesTarget.appendChild(typingDiv)
    this.scrollToBottom()
  }

  scrollToBottom() {
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
  }

  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  getLogoSvg() {
    return document.querySelector('.fancy-border-outline svg').outerHTML
  }

  triggerFileUpload() {
    if (this.hasFileInputTarget) {
      this.fileInputTarget.click()
    }
  }

  handleFileSelect() {
    const file = this.fileInputTarget.files[0]
    if (file) {
      this.handleFileUpload()
    }
  }

  handleFileUpload() {
    const file = this.fileInputTarget.files[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'text/csv', 'application/csv']
    const isCSV = file.name.toLowerCase().endsWith('.csv')

    if (!allowedTypes.includes(file.type) && !isCSV) {
      alert('Please upload an image (JPG, PNG, WEBP) or CSV file.')
      this.fileInputTarget.value = ''
      return
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      alert('File size must be less than 10MB.')
      this.fileInputTarget.value = ''
      return
    }

    // Add file upload message
    this.addFileMessage(file.name)
    this.setLoading()

    // Clear the file input after processing
    this.fileInputTarget.value = ''
  }

  addFileMessage(filename) {
    const messageDiv = document.createElement('div')
    messageDiv.className = 'flex gap-3'

    messageDiv.innerHTML = `
        <div class="flex-auto"></div>
        <div class="ml-12 rounded-lg bg-gray-100 p-3 max-w-[80%]">
          📎 Uploaded file: ${this.escapeHtml(filename)}
        </div>
      `

    this.messagesTarget.appendChild(messageDiv)
    this.scrollToBottom()
  }
}
