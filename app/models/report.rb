# == Schema Information
#
# Table name: reports
#
#  id                   :bigint           not null, primary key
#  description          :text
#  dismissed            :boolean          default(FALSE)
#  dismissed_by_carrier :boolean          default(FALSE)
#  reason               :string(255)
#  created_at           :datetime
#  updated_at           :datetime
#  review_id            :integer
#  user_id              :bigint
#
# Indexes
#
#  index_reports_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_022190ea4e  (user_id => users.id) ON DELETE => cascade
#  fk_rails_8f5a68cdf0  (review_id => reviews.id) ON DELETE => cascade
#
class Report < ApplicationRecord
  REASONS = [
    'Inaccurate information',
    'Language violates our rules',
    'Never worked with this broker'
  ].freeze

  belongs_to :review
  belongs_to :user

  validates :reason, inclusion: { in: REASONS }
  validates :description, presence: true
end
