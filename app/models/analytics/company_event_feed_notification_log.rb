# == Schema Information
#
# Table name: analytics_company_event_feed_notification_logs
#
#  id                   :bigint           not null, primary key
#  payload              :jsonb
#  response             :jsonb
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  analytics_company_id :bigint
#  notification_id      :bigint           not null
#
# Indexes
#
#  idx_on_analytics_company_id_c8276dcbe3  (analytics_company_id)
#  idx_on_event_id_2dca947432              (event_id)
#  idx_on_notification_id_00c18b2bce       (notification_id)
#
# Foreign Keys
#
#  fk_rails_0e0f61a48d  (notification_id => analytics_company_event_feed_notifications.id) ON DELETE => cascade
#  fk_rails_8472c34d36  (analytics_company_id => analytics_companies.id) ON DELETE => cascade
#
module Analytics
  class CompanyEventFeedNotificationLog < ApplicationRecord
    self.ignored_columns += %w(event_id)

    belongs_to :notification, class_name: 'Analytics::CompanyEventFeedNotification'
    belongs_to :analytics_company, class_name: 'Analytics::Company'
  end
end
