# == Schema Information
#
# Table name: webhooks_events
#
#  id         :bigint           not null, primary key
#  data       :jsonb            not null
#  type       :string           not null
#  uuid       :uuid             not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_webhooks_events_on_uuid  (uuid) UNIQUE
#
module Webhooks
  class Event < ApplicationRecord
    include Wisper::ActiveRecord::Publisher

    self.inheritance_column = :_type_disabled
  end
end
