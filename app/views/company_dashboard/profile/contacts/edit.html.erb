<%= turbo_frame_tag 'modal' do %>
  <%= render ModalComponent.new(title: 'Edit Contact', class: 'w-5/6 md:w-1/2 xl:w-1/3') do %>
    <%= render 'company_dashboard/profile/contacts/form',
               model: @contact, entity: @profile.entity,
               available_types: [@contact.type] + @profile.available_contact_types,
               url: url_for([@profile.entity, :dashboard, :profile, :contact, id: @contact]) %>
  <% end %>
<% end %>
