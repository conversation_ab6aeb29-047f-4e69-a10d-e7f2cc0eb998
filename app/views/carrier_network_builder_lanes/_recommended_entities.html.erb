<div id="<%= dom_id(lane, 'recommended_entities') %>">
  <% if lane.refresh_pending? %>
    <div class="m-4 ml-9 p-8 text-center text-gray-500 border border-gray-200 rounded">
      <%= svg_tag 'truck-moving-regular', class: 'animate-spin inline-block h-5 w-5 fill-gray-500 mr-2' %>
      Finding carriers and brokerages for this lane...
    </div>

    <div data-controller="turbo-stream-poll"
         data-turbo-stream-poll-endpoint-value="<%= carrier_network_builder_lane_entities_url(lane.carrier_network_builder.uuid, lane) %>">
    </div>
  <% else %>
    <div class="m-4 ml-9 grid items-center border-x border-gray-200 text-xs border-t border-gray-200
                  grid-cols-[min-content_min-content_auto_1fr_min-content_min-content]">
      <% lane.entities.order(:row_order).map { |le| Companies::EntityClassRegistry.lookup(entity: le.entity, type: :view_model).new(le.entity) }.each do |model| %>
        <div class="group/item contents divide-y divide-gray-200" data-controller="dataset">
          <div class="flex items-center px-3 h-18" data-action="click->dataset#toggle">
            <%= svg_tag 'triangle-solid',
                        class: 'fill-gray-500 inline-block h-1.5 w-3 rotate-90 group-data-[open]/item:rotate-180 cursor-pointer',
                        preserveAspectRatio: 'none' %>
          </div>

          <% if model.entity_type == :carrier %>
            <div class="flex items-center border-x border-gray-200 px-3 bg-primary-50 h-18">
              <%= svg_tag 'truck-regular', class: 'fill-primary inline-block h-4 w-4' %>
            </div>
          <% else %>
            <div class="flex items-center border-x border-gray-200 px-3 bg-purple-50 h-18">
              <%= svg_tag 'circle-nodes-regular', class: 'fill-purple inline-block h-4 w-4' %>
            </div>
          <% end %>

          <div class="flex flex-col justify-center px-3 h-18">
            <h4 class="text-black truncate"><%= model.name %></h4>
            <h5>USDOT: <%= model.dot_number %></h5>
          </div>

          <div class="flex items-center whitespace-nowrap px-3 h-18">
            <div class="-mr-4 -ml-6 scale-60">
              <%= render StarsComponent.new(rating: model.star_rating, class: 'fill-secondary') %>
            </div>
            <div><strong><%= model.star_rating %></strong> (<%= model.review_count %>)</div>
          </div>

          <div class="flex flex-col items-center justify-center px-3 h-18">
            <div><%= model.power_units %></div>
            <div class="text-2xs"><%= 'truck'.pluralize(model.power_units) %></div>
          </div>

          <div class="flex cursor-pointer items-center border-l border-gray-200 px-3 h-18 hover:bg-primary-50">
            <%= link_to url_for([model.entity, :contact]), rel: 'noindex nofollow', data: { turbo_frame: 'modal' } do %>
              <%= svg_tag 'phone-regular', class: 'h-4 w-4 fill-gray-600' %>
            <% end %>
          </div>

          <!-- Expanded Row -->
          <div class="h-full hidden group-data-[open]/item:block"></div>
          <div class="hidden group-data-[open]/item:flex col-span-5 flex-col border-b border-gray-200 bg-white py-4 pr-4">
            <div class="flex gap-6">
              <dl class="flex w-1/2 flex-col gap-1">
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Fleet Size</dt>
                  <dd><%= pluralize(model.power_units, 'truck') %></dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">MCS-150 Mileage</dt>
                  <dd><%= number_with_delimiter(model.mcs_150_mileage.to_i) %></dd>
                </div>
              </dl>

              <dl class="flex w-1/2 flex-col gap-1">
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Authority</dt>
                  <dd>
                    <%= model.authority.title %> <%= model.authority.date ? "(#{l(model.authority.date, format: :mdy)})" : nil %>
                  </dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Insurance</dt>
                  <dd><%= model.insurance_limit %></dd>
                </div>
                <div class="flex items-center gap-1">
                  <dt class="font-semibold text-black">Safety</dt>
                  <dd><%= model.safety_rating %></dd>
                </div>
              </dl>
            </div>

            <div class="mt-3 flex gap-6 border-t border-gray-200 pt-3">
              <% %w(freights truck_types shipment_types specialized_services).each do |method| %>
                <% services = model.public_send(method) %>
                <% if services.present? %>
                  <dl class="w-1/4">
                    <dt class="font-semibold text-black"><%= t("helpers.label.carrier.#{method}") %></dt>
                    <% services.each do |item| %>
                      <dd><%= item.name %></dd>
                    <% end %>
                  </dl>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% end %>
</div>
