module Aws
  module CloudWatch
    class FetchPaginatedMetricData
      include Callable

      attr_reader :metric_data_queries, :start_time, :end_time, :block

      # :nocov:
      def initialize(metric_data_queries:, start_time:, end_time:, &block)
        @metric_data_queries = metric_data_queries
        @start_time = start_time
        @end_time = end_time
        @block = block
      end

      def call
        next_token = nil

        loop do
          response = perform_request(next_token)
          block.call(response)
          next_token = response.next_token
          break if next_token.blank?
        end
      end

      private

      def client
        @client ||= Aws::CloudWatch::Client.new
      end

      def perform_request(next_token = nil)
        client.get_metric_data({
          metric_data_queries:,
          start_time:,
          end_time:,
          next_token:
        }.compact_blank)
      end
      # :nocov:
    end
  end
end
