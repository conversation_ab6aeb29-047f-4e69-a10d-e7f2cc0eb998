require 'rails_helper'

RSpec.describe 'CarrierNetworkBuilders File Upload', type: :request do
  let(:user) { create(:user) }
  let(:carrier_network_builder) { create(:carrier_network_builder, user: user) }

  before do
    # Mock authentication by setting current_user
    allow_any_instance_of(CarrierNetworkBuildersController).to receive(:current_user).and_return(user)
    allow_any_instance_of(CarrierNetworkBuildersController).to receive(:require_login).and_return(true)
  end

  describe 'POST /carrier_network_builders/:id/chat' do
    let(:url) { chat_carrier_network_builder_path(carrier_network_builder.uuid) }

    context 'with valid image file' do
      let(:file) do
        fixture_file_upload(
          Rails.root.join('spec', 'fixtures', 'files', 'test_image.jpg'),
          'image/jpeg'
        )
      end

      before do
        # Create a test image file
        FileUtils.mkdir_p(Rails.root.join('spec', 'fixtures', 'files'))
        File.write(
          Rails.root.join('spec', 'fixtures', 'files', 'test_image.jpg'),
          'fake image content'
        )

        allow(CarrierNetworkBuilders::ProcessFileJob).to receive(:perform_async)

        # Create a mock handler that calls the job when lanes is accessed
        mock_handler = double('handler')
        allow(mock_handler).to receive(:message).and_return({
          content: 'I\'ve received your file. Let me analyze it to extract shipping lane information...',
          role: 'assistant'
        })
        allow(mock_handler).to receive(:lanes) do
          CarrierNetworkBuilders::ProcessFileJob.perform_async(carrier_network_builder.id, 123)
          []
        end

        allow(CarrierNetworkBuilders::ChatResponseHandlers::FileUpload).to receive(:new).and_return(mock_handler)
      end

      after do
        FileUtils.rm_f(Rails.root.join('spec', 'fixtures', 'files', 'test_image.jpg'))
      end

      it 'accepts image file upload' do
        expect do
          post url, params: { file: file }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        end.to change(carrier_network_builder.messages, :count).by(2) # User message + assistant message

        expect(response).to have_http_status(:ok)
        expect(response.content_type).to include('text/vnd.turbo-stream.html')

        # Check user message
        user_message = carrier_network_builder.messages.where(role: 'user').last
        expect(user_message.content).to include('📎 Uploaded file: test_image.jpg')

        # Check assistant message
        assistant_message = carrier_network_builder.messages.where(role: 'assistant').last
        expect(assistant_message.content).to include('analyze it to extract shipping lane information')

        # Check that background job was queued
        expect(CarrierNetworkBuilders::ProcessFileJob).to have_received(:perform_async)
      end
    end

    context 'with valid CSV file' do
      let(:file) do
        fixture_file_upload(
          Rails.root.join('spec', 'fixtures', 'files', 'test_lanes.csv'),
          'text/csv'
        )
      end

      before do
        FileUtils.mkdir_p(Rails.root.join('spec', 'fixtures', 'files'))
        File.write(
          Rails.root.join('spec', 'fixtures', 'files', 'test_lanes.csv'),
          "Origin,Destination,Volume\nChicago IL,Los Angeles CA,10 pallets"
        )

        allow(CarrierNetworkBuilders::ProcessFileJob).to receive(:perform_async)
        allow(CarrierNetworkBuilders::ChatResponseHandlers::FileUpload).to receive(:new).and_return(
          double('handler', message: { content: 'Processing file...', role: 'assistant' }, lanes: [])
        )
      end

      after do
        FileUtils.rm_f(Rails.root.join('spec', 'fixtures', 'files', 'test_lanes.csv'))
      end

      it 'accepts CSV file upload' do
        expect do
          post url, params: { file: file }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        end.to change(carrier_network_builder.messages, :count).by(2)

        expect(response).to have_http_status(:ok)

        user_message = carrier_network_builder.messages.where(role: 'user').last
        expect(user_message.content).to include('📎 Uploaded file: test_lanes.csv')
      end
    end

    context 'with invalid file type' do
      let(:file) do
        fixture_file_upload(
          Rails.root.join('spec', 'fixtures', 'files', 'test_document.pdf'),
          'application/pdf'
        )
      end

      before do
        FileUtils.mkdir_p(Rails.root.join('spec', 'fixtures', 'files'))
        File.write(
          Rails.root.join('spec', 'fixtures', 'files', 'test_document.pdf'),
          'fake pdf content'
        )
      end

      after do
        FileUtils.rm_f(Rails.root.join('spec', 'fixtures', 'files', 'test_document.pdf'))
      end

      it 'rejects invalid file type' do
        expect do
          post url, params: { file: file }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        end.to raise_error(ArgumentError, /Unsupported file type/)
      end
    end

    context 'with text message' do
      before do
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:call)
          .and_return(double(message: double(id: 1), lanes: []))
      end

      it 'still handles text messages normally' do
        expect do
          post url, params: { message: 'Hello' }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        end.to change(carrier_network_builder.messages, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(CarrierNetworkBuilders::ShipperProspectingChatbot).to have_received(:call)
      end
    end

    context 'with no message or file' do
      it 'returns no content' do
        post url, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        expect(response).to have_http_status(:no_content)
      end
    end
  end
end
