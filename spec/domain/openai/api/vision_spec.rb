require 'rails_helper'

RSpec.describe OpenAI::Api::Vision do
  let(:vision) { described_class.new }
  let(:image_url) { 'https://example.com/image.jpg' }
  let(:prompt) { 'Analyze this image for shipping lane information' }

  describe '#analyze_image' do
    let(:response_body) do
      {
        id: 'chatcmpl-123',
        object: 'chat.completion',
        created: 1_677_652_288,
        model: 'gpt-4o',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: 'I can see shipping information in this image...'
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 50,
          completion_tokens: 25,
          total_tokens: 75
        }
      }.to_json
    end

    context 'when request succeeds' do
      let!(:stub) do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .and_return(status: 200, body: response_body, headers: { 'Content-Type' => 'application/json' })
      end

      it 'analyzes image with URL' do
        response = vision.analyze_image(image_url: image_url, prompt: prompt)
        expect(response.status).to eq(200)
        expect(stub).to have_been_requested
      end
    end

    context 'when request fails' do
      let!(:stub) do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .and_return(status: 400, body: '{"error": "Bad request"}')
      end

      it 'handles error gracefully when raise_errors is false' do
        response = vision.analyze_image(image_url: image_url, prompt: prompt)
        expect(response.status).to eq(400)
      end

      it 'raises error when raise_errors is true' do
        vision_with_errors = described_class.new(raise_errors: true)
        expect do
          vision_with_errors.analyze_image(image_url: image_url, prompt: prompt)
        end.to raise_error(OpenAI::Api::Error)
      end
    end
  end

  describe '#analyze_image_with_base64' do
    let(:image_data) { Base64.strict_encode64('fake image data') }

    let!(:stub) do
      stub_request(:post, 'https://api.openai.com/v1/chat/completions')
        .and_return(status: 200, body: '{"choices": [{"message": {"content": "Analysis complete"}}]}')
    end

    it 'analyzes image with base64 data' do
      response = vision.analyze_image_with_base64(image_data: image_data, prompt: prompt)
      expect(response.status).to eq(200)
      expect(stub).to have_been_requested
    end
  end
end
