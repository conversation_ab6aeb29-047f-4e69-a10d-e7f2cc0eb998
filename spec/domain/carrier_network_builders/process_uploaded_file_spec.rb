require 'rails_helper'

RSpec.describe CarrierNetworkBuilders::ProcessUploadedFile do
  let(:builder) { create(:carrier_network_builder) }
  let(:processor) { described_class.new(builder, file_blob) }

  describe '#call' do
    context 'with image file' do
      let(:file_blob) do
        double(
          'ActiveStorage::Blob',
          content_type: 'image/jpeg',
          download: 'fake image data',
          filename: double(to_s: 'lanes.jpg')
        )
      end

      let(:vision_response) do
        double(
          'HTTP::Response',
          parse: {
            'choices' => [
              {
                'message' => {
                  'content' => '```json
[
  {
    "origin_id": "united-states:illinois:chicago",
    "destination_id": "united-states:california:los-angeles",
    "truck_type": ["dry-van"],
    "shipment_type": ["full-truckload"],
    "specialized_service": [],
    "freight": ["general-freight"],
    "volume": "10 pallets",
    "frequency": "weekly"
  }
]
```'
                }
              }
            ]
          }
        )
      end

      before do
        allow(OpenAI::Api::Vision).to receive(:new).and_return(
          double('vision_client', analyze_image_with_base64: vision_response)
        )
      end

      it 'processes image file and returns lane data' do
        result = processor.call
        
        expect(result).to be_an(Array)
        expect(result.size).to eq(1)
        
        lane = result.first
        expect(lane[:origin_id]).to eq('united-states:illinois:chicago')
        expect(lane[:destination_id]).to eq('united-states:california:los-angeles')
        expect(lane[:volume]).to eq('10 pallets')
        expect(lane[:frequency]).to eq('weekly')
        expect(lane[:filters][:truck_type]).to eq(['dry-van'])
      end
    end

    context 'with CSV file' do
      let(:file_blob) do
        double(
          'ActiveStorage::Blob',
          content_type: 'text/csv',
          download: "Origin,Destination,Volume\nChicago IL,Los Angeles CA,5 pallets",
          filename: double(to_s: 'lanes.csv')
        )
      end

      let(:chat_response) do
        double(
          'HTTP::Response',
          parse: {
            'choices' => [
              {
                'message' => {
                  'content' => '[{"origin_id": "united-states:illinois:chicago", "destination_id": "united-states:california:los-angeles", "volume": "5 pallets", "filters": {"truck_type": ["dry-van"]}}]'
                }
              }
            ]
          }
        )
      end

      before do
        allow(OpenAI::Api::ChatCompletion).to receive(:new).and_return(
          double('chat_client', create: chat_response)
        )
      end

      it 'processes CSV file and returns lane data' do
        result = processor.call
        
        expect(result).to be_an(Array)
        expect(result.size).to eq(1)
        
        lane = result.first
        expect(lane[:origin_id]).to eq('united-states:illinois:chicago')
        expect(lane[:destination_id]).to eq('united-states:california:los-angeles')
        expect(lane[:volume]).to eq('5 pallets')
      end
    end

    context 'with unsupported file type' do
      let(:file_blob) do
        double(
          'ActiveStorage::Blob',
          content_type: 'application/pdf',
          filename: double(to_s: 'document.pdf')
        )
      end

      it 'raises ArgumentError for unsupported file type' do
        expect { processor.call }.to raise_error(ArgumentError, /Unsupported file type/)
      end
    end

    context 'when AI returns invalid JSON' do
      let(:file_blob) do
        double(
          'ActiveStorage::Blob',
          content_type: 'image/jpeg',
          download: 'fake image data',
          filename: double(to_s: 'lanes.jpg')
        )
      end

      let(:vision_response) do
        double(
          'HTTP::Response',
          parse: {
            'choices' => [
              {
                'message' => {
                  'content' => 'Invalid JSON response'
                }
              }
            ]
          }
        )
      end

      before do
        allow(OpenAI::Api::Vision).to receive(:new).and_return(
          double('vision_client', analyze_image_with_base64: vision_response)
        )
        allow(Rails.logger).to receive(:error)
      end

      it 'returns empty array and logs error' do
        result = processor.call
        
        expect(result).to eq([])
        expect(Rails.logger).to have_received(:error).with(/Failed to parse AI response as JSON/)
      end
    end
  end
end
