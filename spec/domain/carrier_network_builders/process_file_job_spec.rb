require 'rails_helper'

RSpec.describe CarrierNetworkBuilders::ProcessFileJob do
  let(:builder) { create(:carrier_network_builder) }
  let(:file_blob) { create_file_blob }

  def create_file_blob
    ActiveStorage::Blob.create_and_upload!(
      io: StringIO.new('fake file content'),
      filename: 'test.jpg',
      content_type: 'image/jpeg'
    )
  end

  describe '#perform' do
    context 'when processing succeeds' do
      let(:lanes_data) do
        [
          {
            origin_id: 'united-states:illinois:chicago',
            destination_id: 'united-states:california:los-angeles',
            volume: '10 pallets',
            filters: { truck_type: ['dry-van'] }
          }
        ]
      end

      before do
        allow(CarrierNetworkBuilders::ProcessUploadedFile).to receive(:call)
          .and_return(lanes_data)
        allow(CarrierNetworkBuilders::UpsertLane).to receive(:call)
          .and_return(double('lane', id: 1))
      end

      it 'processes file and creates lanes' do
        expect do
          described_class.perform_inline(builder.id, file_blob.id)
        end.to change(builder.messages, :count).by(1) # Success message

        expect(CarrierNetworkBuilders::ProcessUploadedFile)
          .to have_received(:call).with(builder, file_blob)
        expect(CarrierNetworkBuilders::UpsertLane)
          .to have_received(:call).with(builder, lanes_data.first)
      end

      it 'creates appropriate success message for single lane' do
        described_class.perform_inline(builder.id, file_blob.id)
        
        success_message = builder.messages.where(role: 'assistant').last
        expect(success_message.content).to include('I found 1 shipping lane')
      end

      it 'creates appropriate success message for multiple lanes' do
        allow(CarrierNetworkBuilders::ProcessUploadedFile).to receive(:call)
          .and_return([lanes_data.first, lanes_data.first])
        
        described_class.perform_inline(builder.id, file_blob.id)
        
        success_message = builder.messages.where(role: 'assistant').last
        expect(success_message.content).to include('I found 2 shipping lanes')
      end

      it 'purges the file blob after processing' do
        allow(ActiveStorage::Blob).to receive(:find_by).with(id: file_blob.id).and_return(file_blob)
        expect(file_blob).to receive(:purge_later)
        described_class.perform_inline(builder.id, file_blob.id)
      end
    end

    context 'when no lanes are found' do
      before do
        allow(CarrierNetworkBuilders::ProcessUploadedFile).to receive(:call)
          .and_return([])
      end

      it 'creates error message when no lanes found' do
        described_class.perform_inline(builder.id, file_blob.id)
        
        error_message = builder.messages.where(role: 'assistant').last
        expect(error_message.content).to include("couldn't find any clear shipping lane information")
      end
    end

    context 'when processing fails' do
      before do
        allow(CarrierNetworkBuilders::ProcessUploadedFile).to receive(:call)
          .and_raise(StandardError, 'Processing failed')
        allow(Rails.logger).to receive(:error)
      end

      it 'creates error message and logs error' do
        described_class.perform_inline(builder.id, file_blob.id)
        
        error_message = builder.messages.where(role: 'assistant').last
        expect(error_message.content).to include('encountered an error while processing')
        expect(Rails.logger).to have_received(:error).with(/Processing failed/)
      end

      it 'still purges the file blob' do
        allow(ActiveStorage::Blob).to receive(:find_by).with(id: file_blob.id).and_return(file_blob)
        expect(file_blob).to receive(:purge_later)
        described_class.perform_inline(builder.id, file_blob.id)
      end
    end

    context 'when builder does not exist' do
      it 'does not raise error' do
        expect do
          described_class.perform_inline(-1, file_blob.id)
        end.not_to raise_error
      end
    end

    context 'when file blob does not exist' do
      it 'does not raise error' do
        expect do
          described_class.perform_inline(builder.id, -1)
        end.not_to raise_error
      end
    end
  end
end
