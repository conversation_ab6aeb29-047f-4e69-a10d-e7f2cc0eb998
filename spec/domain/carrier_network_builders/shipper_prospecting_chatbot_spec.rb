require 'rails_helper'

RSpec.describe CarrierNetworkBuilders::ShipperProspectingChatbot do
  subject(:chatbot) { described_class.new(builder) }

  describe '#call' do
    let(:builder) { create :carrier_network_builder }

    before do
      stub_request(:post, 'https://api.openai.com/v1/chat/completions')
        .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
    end

    context 'when lane requirements have been met' do
      let(:requirements) do
        <<~JSON
          { "origin_id": "united-states:california", "destination_id": "united-states:utah" }
        JSON
      end

      let(:body) do
        {
          id: 'chatcmpl-123',
          object: 'chat.completion',
          created: 1_677_652_288,
          model: 'gpt-4o-mini',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: "Here are the requirements:\n```json\n#{requirements}\n```"
              },
              finish_reason: 'stop'
            }
          ],
          usage: {
            prompt_tokens: 9,
            completion_tokens: 12,
            total_tokens: 21
          }
        }.to_json
      end

      it 'creates a lane' do
        expect { chatbot.call }.to change(CarrierNetworkBuilderLane, :count).by(1)
      end

      it 'creates a message' do
        expect { chatbot.call }.to change(CarrierNetworkBuilderMessage, :count).by(1)
      end
    end

    context 'when lane requirements have not been met' do
      let(:body) do
        {
          id: 'chatcmpl-123',
          object: 'chat.completion',
          created: 1_677_652_288,
          model: 'gpt-4o-mini',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'Hello! How can I help you today?'
              },
              finish_reason: 'stop'
            }
          ],
          usage: {
            prompt_tokens: 9,
            completion_tokens: 12,
            total_tokens: 21
          }
        }.to_json
      end

      it 'creates a new message in the builder' do
        expect { chatbot.call }.to change(CarrierNetworkBuilderMessage, :count).by(1)
        expect(builder.reload.messages.last.content).to eq('Hello! How can I help you today?')
      end
    end
  end
end
